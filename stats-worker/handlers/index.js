/**
 * Job Handlers Registry
 * Central registry for all background job handlers
 */

const StatsHandler = require('./StatsHandler');
const HealthHandler = require('./HealthHandler');
const CleanupHandler = require('./CleanupHandler');
const EmailHandler = require('./EmailHandler');

/**
 * Registry of all job handlers
 */
const handlers = new Map();

// Stats handlers
handlers.set('calculate-daily-stats', StatsHandler.calculateDailyStats);
handlers.set('calculate-weekly-stats', StatsHandler.calculateWeeklyStats);
handlers.set('calculate-monthly-stats', StatsHandler.calculateMonthlyStats);
handlers.set('cleanup-old-stats', StatsHandler.cleanupOldStats);

// Health check handlers
handlers.set('check-database-health', HealthHandler.checkDatabaseHealth);
handlers.set('check-api-health', HealthHandler.checkApiHealth);
handlers.set('check-memory-usage', HealthHandler.checkMemoryUsage);
handlers.set('check-external-services', HealthHandler.checkExternalServices);
handlers.set('check-frontend-health', HealthHandler.checkFrontendHealth);
handlers.set('comprehensive-health-check', HealthHandler.comprehensiveHealthCheck);

// Cleanup handlers
handlers.set('cleanup-old-logs', CleanupHandler.cleanupOldLogs);
handlers.set('cleanup-expired-sessions', CleanupHandler.cleanupExpiredSessions);
handlers.set('cleanup-old-jobs', CleanupHandler.cleanupOldJobs);
handlers.set('optimize-database', CleanupHandler.optimizeDatabase);
handlers.set('backup-critical-data', CleanupHandler.backupCriticalData);

// Email handlers
handlers.set('send-daily-digest', EmailHandler.sendDailyDigest);
handlers.set('send-weekly-report', EmailHandler.sendWeeklyReport);
handlers.set('send-alert-notification', EmailHandler.sendAlertNotification);

/**
 * Get handler for a specific job type
 */
function getHandler(jobType) {
  const handler = handlers.get(jobType);
  
  if (!handler) {
    throw new Error(`No handler registered for job type: ${jobType}`);
  }

  return {
    execute: async (jobData, job) => {
      try {
        console.log(`🔧 Executing handler for job type: ${jobType}`);
        const result = await handler(jobData, job);
        console.log(`✅ Handler completed for job type: ${jobType}`);
        return result;
      } catch (error) {
        console.error(`❌ Handler failed for job type: ${jobType}:`, error);
        throw error;
      }
    }
  };
}

/**
 * Register a new job handler
 */
function registerHandler(jobType, handlerFunction) {
  if (handlers.has(jobType)) {
    console.warn(`⚠️ Overwriting existing handler for job type: ${jobType}`);
  }
  
  handlers.set(jobType, handlerFunction);
  console.log(`📝 Registered handler for job type: ${jobType}`);
}

/**
 * Get all registered job types
 */
function getRegisteredJobTypes() {
  return Array.from(handlers.keys());
}

/**
 * Check if a handler exists for a job type
 */
function hasHandler(jobType) {
  return handlers.has(jobType);
}

/**
 * Get handler statistics
 */
function getHandlerStats() {
  const stats = {
    totalHandlers: handlers.size,
    handlersByCategory: {
      stats: 0,
      health: 0,
      cleanup: 0,
      email: 0,
      other: 0,
    },
    registeredTypes: Array.from(handlers.keys()),
  };

  // Categorize handlers
  for (const jobType of handlers.keys()) {
    if (jobType.includes('stats') || jobType.includes('calculate')) {
      stats.handlersByCategory.stats++;
    } else if (jobType.includes('check') || jobType.includes('health')) {
      stats.handlersByCategory.health++;
    } else if (jobType.includes('cleanup') || jobType.includes('optimize') || jobType.includes('backup')) {
      stats.handlersByCategory.cleanup++;
    } else if (jobType.includes('send') || jobType.includes('email') || jobType.includes('digest')) {
      stats.handlersByCategory.email++;
    } else {
      stats.handlersByCategory.other++;
    }
  }

  return stats;
}

module.exports = {
  getHandler,
  registerHandler,
  getRegisteredJobTypes,
  hasHandler,
  getHandlerStats,
};
