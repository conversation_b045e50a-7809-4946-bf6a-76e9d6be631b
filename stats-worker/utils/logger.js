/**
 * Simple Logger Utility for Worker
 * Provides structured logging for the worker system
 */

class Logger {
  static log(level, message, metadata = {}) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message,
      ...metadata,
    };

    // Console output with colors
    const colors = {
      error: '\x1b[31m', // Red
      warn: '\x1b[33m',  // Yellow
      info: '\x1b[36m',  // Cyan
      debug: '\x1b[90m', // Gray
      reset: '\x1b[0m',  // Reset
    };

    const color = colors[level] || colors.reset;
    console.log(`${color}[${timestamp}] WORKER ${level.toUpperCase()}: ${message}${colors.reset}`);
    
    if (Object.keys(metadata).length > 0) {
      console.log(`${color}  Metadata:${colors.reset}`, metadata);
    }

    // Here you could also write to database or file
    // For now, we'll just use console output
  }

  static error(message, metadata = {}) {
    this.log('error', message, metadata);
  }

  static warn(message, metadata = {}) {
    this.log('warn', message, metadata);
  }

  static info(message, metadata = {}) {
    this.log('info', message, metadata);
  }

  static debug(message, metadata = {}) {
    if (process.env.NODE_ENV === 'development' || process.env.DEBUG === 'true') {
      this.log('debug', message, metadata);
    }
  }
}

module.exports = { Logger };
