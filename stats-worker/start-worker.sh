#!/bin/bash

# HLenergy Background Worker Startup Script

echo "🚀 Starting HLenergy Background Worker..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 16+ to continue."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2)
REQUIRED_VERSION="16.0.0"

if ! node -e "process.exit(require('semver').gte('$NODE_VERSION', '$REQUIRED_VERSION') ? 0 : 1)" 2>/dev/null; then
    echo "❌ Node.js version $NODE_VERSION is not supported. Please upgrade to Node.js 16+ to continue."
    exit 1
fi

# Set environment
export NODE_ENV=${NODE_ENV:-production}

# Check if .env file exists
if [ ! -f "../.env" ]; then
    echo "⚠️ Warning: .env file not found. Using default environment variables."
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing worker dependencies..."
    npm install
fi

# Check if database is accessible
echo "🔌 Checking database connection..."
if ! node -e "
const { initializeDatabase } = require('../shared/models');
initializeDatabase().then(() => {
    console.log('✅ Database connection successful');
    process.exit(0);
}).catch((error) => {
    console.error('❌ Database connection failed:', error.message);
    process.exit(1);
});
"; then
    echo "❌ Cannot connect to database. Please check your database configuration."
    exit 1
fi

# Start the worker
echo "⚙️ Starting worker process..."
echo "📍 Environment: $NODE_ENV"
echo "📍 Process ID: $$"
echo ""

# Use PM2 if available, otherwise use node directly
if command -v pm2 &> /dev/null; then
    echo "🔄 Starting with PM2..."
    pm2 start index.js --name "hlenergy-worker" --env $NODE_ENV
    pm2 logs hlenergy-worker
else
    echo "🔄 Starting with Node.js..."
    node index.js
fi
