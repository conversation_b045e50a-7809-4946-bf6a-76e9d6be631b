{"name": "hlenergy-worker", "version": "1.0.0", "description": "HLenergy Background Worker Process", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "build": "echo 'Worker build completed'", "test": "echo 'Worker tests not implemented yet'"}, "keywords": ["background-jobs", "worker", "scheduler", "health-monitoring", "statistics"], "author": "HLenergy Team", "license": "MIT", "dependencies": {"bcryptjs": "^3.0.2", "dotenv": "^17.2.1", "mysql2": "^3.6.5", "node-cron": "^4.2.1", "sequelize": "^6.37.7"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}