{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc", "start": "node shared/index.js", "dev": "nodemon shared/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:all": "node tests/run-all-tests.js", "test:endpoints": "jest tests/integration/endpoint-availability.test.js", "test:functionality": "jest tests/integration/all-endpoints.test.js", "test:performance": "jest tests/integration/endpoint-performance.test.js", "postman:generate": "node toolss/generate-postman-collection.js", "version:status": "node scripts/version-bump.js status", "version:patch": "node scripts/version-bump.js patch --changelog", "version:minor": "node scripts/version-bump.js minor --changelog", "version:major": "node scripts/version-bump.js major --changelog", "version:tag": "node scripts/version-bump.js patch --changelog --tag", "version:dry-run": "node scripts/version-bump.js patch --dry-run", "version:init": "./toolss/init-version.sh", "deps:check": "./toolss/check-updates.sh check", "deps:outdated": "./toolss/check-updates.sh outdated", "deps:security": "./toolss/check-updates.sh security", "deps:update": "./toolss/check-updates.sh interactive", "deps:report": "./toolss/check-updates.sh report", "db:setup": "./toolss/db-setup.sh setup", "db:create": "./toolss/db-setup.sh create", "db:migrate": "./toolss/db-setup.sh migrate", "db:seed": "./toolss/db-setup.sh seed", "db:status": "./toolss/db-setup.sh status", "db:reset": "./toolss/db-setup.sh reset", "db:backup": "./toolss/db-setup.sh backup", "logs:view": "tail -f logs/combined-$(date +%Y-%m-%d).log", "logs:errors": "tail -f logs/error-$(date +%Y-%m-%d).log", "logs:requests": "tail -f logs/requests-$(date +%Y-%m-%d).log", "logs:clean": "find logs -name '*.log' -mtime +30 -delete", "create-admin": "node toolss/create-admin.js", "auth:test": "echo 'Run npm run create-admin first, then use the provided curl commands'"}, "keywords": ["api", "energy", "consultation", "express", "jwt", "swagger"], "author": "HLenergy Team", "license": "ISC", "description": "HLenergy Backend API for energy consultation services", "dependencies": {"@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-slow-down": "^3.0.0", "express-validator": "^7.2.1", "firebase": "^12.0.0", "firebase-admin": "^13.4.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "multer": "^2.0.2", "mysql2": "^3.14.2", "node": "^22.18.0", "nodemailer": "^7.0.5", "redoc-express": "^2.1.0", "sequelize": "^6.37.7", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "web-push": "^3.6.7", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/morgan": "^1.9.10", "@types/node": "^24.0.15", "axios": "^1.10.0", "nodemon": "^3.1.10", "sequelize-cli": "^6.6.3", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}