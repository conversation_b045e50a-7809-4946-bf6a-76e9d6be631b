{"name": "hlenergy-workers", "version": "1.0.0", "description": "Unified worker processes for HLenergy", "scripts": {"start": "node index.js", "start:email": "node email/worker.js", "start:stats": "node stats/worker.js", "start:all": "concurrently \"npm run start:email\" \"npm run start:stats\"", "dev": "nodemon index.js", "dev:email": "nodemon email/worker.js", "dev:stats": "nodemon stats/worker.js", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js", "health": "node scripts/health-check.js", "test": "jest"}, "dependencies": {"concurrently": "^8.2.0", "nodemon": "^3.0.0"}}